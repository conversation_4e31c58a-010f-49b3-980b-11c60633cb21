#!/usr/bin/env python3

"""
Test script to verify XLM-RoBERTa compatibility with RetroMAE
"""

import sys
import os
sys.path.append('src')

from transformers import AutoModelForMaskedLM, AutoTokenizer
from pretrain.modeling import RetroMAEForPretraining
from pretrain.arguments import ModelArguments

def test_xlm_roberta_compatibility():
    print("Testing XLM-RoBERTa compatibility with RetroMAE...")
    
    # Load XLM-RoBERTa model and tokenizer
    model_name = "xlm-roberta-base"
    print(f"Loading {model_name}...")
    
    try:
        hf_model = AutoModelForMaskedLM.from_pretrained(model_name)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"✓ Successfully loaded {model_name}")
        print(f"  Model type: {type(hf_model)}")
        print(f"  Vocab size: {tokenizer.vocab_size}")
        
        # Check if model has the expected attributes
        if hasattr(hf_model, 'roberta'):
            print("✓ Model has 'roberta' attribute")
            print(f"  Embeddings: {type(hf_model.roberta.embeddings)}")
        elif hasattr(hf_model, 'bert'):
            print("✓ Model has 'bert' attribute")
            print(f"  Embeddings: {type(hf_model.bert.embeddings)}")
        else:
            print("✗ Model has neither 'roberta' nor 'bert' attribute")
            return False
            
        # Test RetroMAE initialization
        model_args = ModelArguments()
        retromae_model = RetroMAEForPretraining(hf_model, model_args)
        print("✓ Successfully initialized RetroMAEForPretraining with XLM-RoBERTa")
        
        # Test that base_model is set correctly
        print(f"  Base model type: {type(retromae_model.base_model)}")
        print(f"  Decoder embeddings type: {type(retromae_model.decoder_embeddings)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_xlm_roberta_compatibility()
    if success:
        print("\n🎉 All tests passed! XLM-RoBERTa should work with RetroMAE now.")
    else:
        print("\n❌ Tests failed. There are still compatibility issues.")
    sys.exit(0 if success else 1)
